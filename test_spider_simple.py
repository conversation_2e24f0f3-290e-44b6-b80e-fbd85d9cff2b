# -*- coding: utf-8 -*-
"""
简化版南京市委组织部工作动态爬虫测试
不依赖pandas，直接测试爬取功能
"""
import requests
from bs4 import BeautifulSoup
import re
from urllib.parse import urljoin
import time

class SimpleGzdtSpider:
    """简化版组织部爬虫"""
    
    def __init__(self):
        self.start_url = "https://zzb.nanjing.gov.cn/lgbgz/gzdt/index.html"
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
        }
        
    def fetch_webpage(self, url):
        """获取网页内容"""
        try:
            print(f"正在获取: {url}")
            # 禁用代理
            proxies = {'http': None, 'https': None}
            response = requests.get(url, headers=self.headers, timeout=30, proxies=proxies)
            response.raise_for_status()
            
            if response.encoding == 'ISO-8859-1':
                response.encoding = response.apparent_encoding or 'utf-8'
            elif not response.encoding:
                response.encoding = 'utf-8'
                
            return True, response.text
            
        except Exception as e:
            print(f"获取失败: {str(e)}")
            return False, ""
    
    def extract_links_from_page(self, html_content):
        """从页面提取工作动态链接"""
        try:
            soup = BeautifulSoup(html_content, 'html.parser')
            
            # 查找所有包含链接的li元素
            news_items = soup.find_all('li')
            print(f"找到 {len(news_items)} 个li元素")
            
            valid_links = []
            
            for idx, item in enumerate(news_items, 1):
                try:
                    # 检查是否包含链接
                    link_element = item.find('a', href=True)
                    if not link_element:
                        continue
                        
                    title_text = link_element.get_text().strip()
                    title_link = link_element.get('href')
                    
                    if not title_text or not title_link:
                        continue
                    
                    # 在li元素的文本中查找日期
                    item_text = item.get_text()
                    date_match = re.search(r'2025-(\d{2})-(\d{2})', item_text)
                    
                    if not date_match:
                        continue
                    
                    date_text = date_match.group()
                    month = int(date_match.group(1))
                    
                    # 检查是否在5-7月范围内
                    if month not in [5, 6, 7]:
                        print(f"  跳过: {title_text[:30]}... (日期{date_text}不在范围内)")
                        continue
                    
                    # 处理相对URL
                    if not title_link.startswith('http'):
                        clean_link = title_link.lstrip('./')
                        title_link = urljoin("https://zzb.nanjing.gov.cn/lgbgz/gzdt/", clean_link)
                    
                    valid_links.append({
                        'url': title_link,
                        'title': title_text,
                        'date': date_text,
                    })
                    
                    print(f"  ✅ 找到: {title_text[:50]}... ({date_text})")
                    
                except Exception as e:
                    continue
            
            return valid_links
            
        except Exception as e:
            print(f"提取链接失败: {str(e)}")
            return []
    
    def extract_article_content(self, url):
        """提取文章内容"""
        print(f"  正在解析文章: {url}")

        success, html_content = self.fetch_webpage(url)
        if not success:
            return "", ""

        try:
            soup = BeautifulSoup(html_content, 'html.parser')

            # 提取标题 - 多种方法
            title = ""

            # 方法1: 查找加粗的标题
            bold_title = soup.find('strong')
            if bold_title:
                title = bold_title.get_text().strip()

            # 方法2: 从页面文本中提取标题（在"当前位置"之后，"来源"之前）
            if not title:
                all_text = soup.get_text()
                lines = all_text.split('\n')

                for i, line in enumerate(lines):
                    line = line.strip()
                    # 查找"当前位置"后面的标题
                    if '当前位置：' in line and i + 1 < len(lines):
                        # 标题通常在当前位置后面几行
                        for j in range(i + 1, min(i + 5, len(lines))):
                            potential_title = lines[j].strip()
                            if (len(potential_title) > 5 and
                                '来源:' not in potential_title and
                                '发布时间:' not in potential_title and
                                potential_title not in ['首页', '老干部工作', '工作动态']):
                                title = potential_title
                                break
                        if title:
                            break

            # 方法3: 如果还没找到，尝试其他选择器
            if not title:
                title_selectors = ['h1', 'h2', '.title', '.tit']
                for selector in title_selectors:
                    title_elem = soup.select_one(selector)
                    if title_elem:
                        title = title_elem.get_text().strip()
                        if len(title) > 3:
                            break

            # 提取正文 - 简化方法
            content = ""

            # 直接从页面文本中提取内容
            all_text = soup.get_text()
            lines = all_text.split('\n')
            content_lines = []

            start_collecting = False
            for line in lines:
                line = line.strip()
                if not line:
                    continue

                # 开始收集内容的标志
                if '发布时间:' in line:
                    start_collecting = True
                    continue

                # 停止收集的标志
                if '版权所有' in line or '苏ICP备' in line:
                    break

                # 收集有效内容
                if start_collecting and len(line) > 15:
                    # 过滤掉一些无用信息
                    if (line not in ['搜索', '首页', '组织建设', '干部工作', '公务员工作', '人才工作', '老干部工作'] and
                        '欢迎光临' not in line and
                        'javascript:' not in line):
                        content_lines.append(line)

            content = '\n\n'.join(content_lines)

            print(f"    提取结果: 标题长度={len(title)}, 内容长度={len(content)}")
            if len(content) < 50:
                print(f"    内容太短，显示前200字符: {all_text[:200]}")

            return title, content

        except Exception as e:
            print(f"    解析失败: {str(e)}")
            return "", ""
    
    def test_crawl(self):
        """测试爬取功能"""
        print("🚀 开始测试南京市委组织部工作动态爬取...")
        print("=" * 60)
        
        # 获取首页
        success, html_content = self.fetch_webpage(self.start_url)
        if not success:
            print("❌ 无法获取首页")
            return
        
        # 提取链接
        articles = self.extract_links_from_page(html_content)
        print(f"\n📋 共找到 {len(articles)} 条符合条件的文章")
        
        if not articles:
            print("⚠️ 未找到任何文章，可能需要调整解析逻辑")
            return
        
        # 测试提取前3篇文章的内容
        print("\n📄 开始提取文章内容...")
        for i, article in enumerate(articles[:3], 1):
            print(f"\n--- 第{i}篇文章 ---")
            print(f"标题: {article['title']}")
            print(f"日期: {article['date']}")
            print(f"链接: {article['url']}")
            
            title, content = self.extract_article_content(article['url'])
            if content and len(content) > 50:  # 只要有内容就算成功
                print(f"✅ 内容提取成功")
                print(f"   标题: {title[:50] if title else '未提取到标题'}...")
                print(f"   正文: {content[:100]}...")
                print(f"   字数: {len(content)}")
            else:
                print("❌ 内容提取失败")
            
            time.sleep(1)  # 避免请求过快
        
        print(f"\n🎉 测试完成！共找到{len(articles)}条记录")
        return articles

if __name__ == '__main__':
    spider = SimpleGzdtSpider()
    articles = spider.test_crawl()
