# -*- coding: utf-8 -*-
"""
测试南京档案馆网站的XPath选择器
"""

import requests
from lxml import html

def test_xpath():
    url = 'https://dag.nanjing.gov.cn/gzdt/index.html'
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
    }

    try:
        response = requests.get(url, headers=headers, timeout=30)
        response.raise_for_status()
        
        tree = html.fromstring(response.text)
        
        print("=== 测试新闻项XPath ===")
        # 测试新闻项XPath
        news_items = tree.xpath('//ul/li[contains(., "2025-")]')
        print(f'找到新闻项数量: {len(news_items)}')
        
        # 如果没找到，尝试其他选择器
        if len(news_items) == 0:
            print("\n=== 尝试其他选择器 ===")
            # 尝试所有li元素
            all_lis = tree.xpath('//li')
            print(f'所有li元素数量: {len(all_lis)}')
            
            # 查找包含日期的元素
            date_elements = tree.xpath('//*[contains(text(), "2025-")]')
            print(f'包含2025-的元素数量: {len(date_elements)}')
            
            # 打印前几个包含日期的元素
            for i, elem in enumerate(date_elements[:3]):
                print(f'日期元素{i+1}: {elem.text_content()[:100]}')
        
        print("\n=== 测试链接提取 ===")
        # 测试链接提取
        links = tree.xpath('//a[contains(@href, ".html")]')
        print(f'找到链接数量: {len(links)}')
        
        # 打印前几个链接
        for i, link in enumerate(links[:5]):
            href = link.get('href', '')
            text = link.text_content().strip()
            print(f'链接{i+1}: {text[:50]} -> {href}')
            
        print("\n=== 分析新闻列表结构 ===")
        # 查找新闻列表的实际结构
        # 从web-fetch的结果看，新闻是在li元素中，格式为：标题链接 + 日期
        news_links = tree.xpath('//li//a[contains(@href, "/gzdt/")]')
        print(f'工作动态链接数量: {len(news_links)}')
        
        for i, link in enumerate(news_links[:5]):
            href = link.get('href', '')
            text = link.text_content().strip()
            # 查找同级的日期文本
            parent_li = link.getparent()
            while parent_li is not None and parent_li.tag != 'li':
                parent_li = parent_li.getparent()
            
            if parent_li is not None:
                li_text = parent_li.text_content()
                print(f'新闻{i+1}: {text} -> {href}')
                print(f'  完整li内容: {li_text.strip()}')
                
    except Exception as e:
        print(f'错误: {e}')

if __name__ == "__main__":
    test_xpath()
