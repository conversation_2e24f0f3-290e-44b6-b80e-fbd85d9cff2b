# -*- coding: utf-8 -*-
"""
修正版南京市委组织部工作动态爬虫 - 简化版（不依赖pandas）
"""
import requests
from bs4 import BeautifulSoup
import re
from urllib.parse import urljoin
import time
import os
import json

class FixedGzdtSpider:
    """修正版组织部爬虫"""
    
    def __init__(self):
        self.start_url = "https://zzb.nanjing.gov.cn/lgbgz/gzdt/index.html"
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
        }
        self.processed_articles = 0
        
    def fetch_webpage(self, url):
        """获取网页内容"""
        try:
            print(f"正在获取: {url}")
            proxies = {'http': None, 'https': None}
            response = requests.get(url, headers=self.headers, timeout=30, proxies=proxies)
            response.raise_for_status()
            
            if response.encoding == 'ISO-8859-1':
                response.encoding = response.apparent_encoding or 'utf-8'
            elif not response.encoding:
                response.encoding = 'utf-8'
                
            return True, response.text
            
        except Exception as e:
            print(f"获取失败: {str(e)}")
            return False, ""
    
    def extract_links_from_page(self, html_content):
        """从页面提取工作动态链接"""
        try:
            soup = BeautifulSoup(html_content, 'html.parser')
            news_items = soup.find_all('li')
            print(f"找到 {len(news_items)} 个li元素")
            
            valid_links = []
            
            for idx, item in enumerate(news_items, 1):
                try:
                    link_element = item.find('a', href=True)
                    if not link_element:
                        continue
                        
                    title_text = link_element.get_text().strip()
                    title_link = link_element.get('href')
                    
                    if not title_text or not title_link:
                        continue
                    
                    # 在li元素的文本中查找日期
                    item_text = item.get_text()
                    date_match = re.search(r'2025-(\d{2})-(\d{2})', item_text)
                    
                    if not date_match:
                        continue
                    
                    date_text = date_match.group()
                    month = int(date_match.group(1))
                    
                    # 检查是否在5-7月范围内
                    if month not in [5, 6, 7]:
                        continue
                    
                    # 处理相对URL
                    if not title_link.startswith('http'):
                        clean_link = title_link.lstrip('./')
                        title_link = urljoin("https://zzb.nanjing.gov.cn/lgbgz/gzdt/", clean_link)
                    
                    valid_links.append({
                        'url': title_link,
                        'title': title_text,
                        'date': date_text,
                        'date_key': f"2025-{month:02d}"
                    })
                    
                    print(f"  ✅ 找到: {title_text[:50]}... ({date_text})")
                    
                except Exception as e:
                    continue
            
            return valid_links
            
        except Exception as e:
            print(f"提取链接失败: {str(e)}")
            return []
    
    def extract_article_content(self, url):
        """提取文章内容"""
        print(f"  正在解析文章: {url}")
        
        success, html_content = self.fetch_webpage(url)
        if not success:
            return "", ""
        
        try:
            soup = BeautifulSoup(html_content, 'html.parser')
            
            # 提取标题
            title = ""
            bold_title = soup.find('strong')
            if bold_title:
                title = bold_title.get_text().strip()
            
            if not title:
                all_text = soup.get_text()
                lines = all_text.split('\n')
                
                for i, line in enumerate(lines):
                    line = line.strip()
                    if '当前位置：' in line and i + 1 < len(lines):
                        for j in range(i + 1, min(i + 5, len(lines))):
                            potential_title = lines[j].strip()
                            if (len(potential_title) > 5 and 
                                '来源:' not in potential_title and
                                '发布时间:' not in potential_title and
                                potential_title not in ['首页', '老干部工作', '工作动态']):
                                title = potential_title
                                break
                        if title:
                            break
            
            # 提取正文
            content = ""
            all_text = soup.get_text()
            lines = all_text.split('\n')
            content_lines = []
            
            start_collecting = False
            for line in lines:
                line = line.strip()
                if not line:
                    continue
                
                if '发布时间:' in line:
                    start_collecting = True
                    continue
                
                if '版权所有' in line or '苏ICP备' in line:
                    break
                
                if start_collecting and len(line) > 15:
                    if (line not in ['搜索', '首页', '组织建设', '干部工作', '公务员工作', '人才工作', '老干部工作'] and
                        '欢迎光临' not in line and
                        'javascript:' not in line):
                        content_lines.append(line)
            
            content = '\n\n'.join(content_lines)
            
            return title, content
            
        except Exception as e:
            print(f"    解析失败: {str(e)}")
            return "", ""
    
    def crawl_all_pages(self):
        """主爬取流程"""
        print("🚀 开始爬取南京市委组织部工作动态...")
        print(f"📝 目标日期: 2025年5-7月")
        print("=" * 60)

        all_articles = []
        
        # 获取首页
        success, html_content = self.fetch_webpage(self.start_url)
        if not success:
            print("❌ 无法获取首页")
            return []
        
        # 提取链接
        articles = self.extract_links_from_page(html_content)
        print(f"\n📋 共找到 {len(articles)} 条符合条件的文章")
        
        if not articles:
            print("⚠️ 未找到任何文章")
            return []
        
        # 处理所有文章
        print("\n📄 开始提取文章内容...")
        for i, article in enumerate(articles, 1):
            print(f"\n--- 第{i}篇文章 ---")
            print(f"标题: {article['title']}")
            print(f"日期: {article['date']}")
            
            title, content = self.extract_article_content(article['url'])
            if content and len(content) > 50:
                final_title = title if title else article['title']
                
                all_articles.append({
                    '标题': final_title,
                    '发布日期': article['date'],
                    '链接': article['url'],
                    '正文': content,
                    '字数': len(content)
                })
                
                self.processed_articles += 1
                print(f"✅ 内容提取成功 (字数: {len(content)})")
            else:
                print("❌ 内容提取失败")
            
            time.sleep(1)  # 避免请求过快
        
        # 保存为JSON文件（不依赖pandas）
        output_file = "spiderdata/南京市委组织部_工作动态_2025年5-7月.json"
        os.makedirs(os.path.dirname(output_file), exist_ok=True)
        
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(all_articles, f, ensure_ascii=False, indent=2)
        
        print(f"\n🎉 爬取完成！共{len(all_articles)}条记录已保存到 {output_file}")
        return all_articles

if __name__ == '__main__':
    spider = FixedGzdtSpider()
    articles = spider.crawl_all_pages()
