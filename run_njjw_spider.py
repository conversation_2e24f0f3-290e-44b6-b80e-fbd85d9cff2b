# -*- coding: utf-8 -*-
"""
南京市纪委监委新闻爬虫启动脚本
专门用于爬取5-7月的要闻动态
"""

import sys
import os
from spider_news_2_1 import NewsSpider

def main():
    """主函数"""
    print("=" * 80)
    print("南京市纪委监委新闻爬虫")
    print("目标网站: https://jw.nanjing.gov.cn/xwzx/yw/")
    print("爬取范围: 2025年5-7月要闻")
    print("=" * 80)
    
    try:
        # 创建爬虫实例
        spider = NewsSpider()
        
        # 显示配置信息
        print(f"\n配置信息:")
        print(f"起始URL: {spider.start_url}")
        print(f"最大页数: {spider.max_pages}")
        print(f"目标日期: {', '.join(spider.target_date_range)}")
        print(f"输出文件: {spider.output_file}")
        
        # 确认开始爬取
        user_input = input("\n是否开始爬取？(y/n): ").strip().lower()
        if user_input not in ['y', 'yes', '是']:
            print("用户取消操作")
            return
        
        # 开始爬取
        print("\n开始爬取...")
        success = spider.run_paginated_crawl()
        
        if success:
            print(f"\n✓ 爬取成功完成！")
            print(f"数据已保存到: {spider.output_file}")
        else:
            print(f"\n✗ 爬取失败")
            
    except KeyboardInterrupt:
        print("\n用户中断操作")
    except Exception as e:
        print(f"\n发生错误: {str(e)}")
        import traceback
        traceback.print_exc()
    finally:
        input("\n按回车键退出...")

if __name__ == "__main__":
    main()
