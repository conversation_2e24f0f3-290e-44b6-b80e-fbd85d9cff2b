'''
Description: 南京市纪委监委新闻爬虫配置文件
Date: 2025-07-17
Author: <PERSON><PERSON><PERSON>
LastEditTime: 2025-07-17 19:51:09
FilePath: \Spider_faxuan\config_news_2_1.py
'''
# -*- coding: utf-8 -*-
"""
南京市纪委监委新闻分页爬虫配置文件
"""

# 起始页面配置
START_URL = "https://jw.nanjing.gov.cn/xwzx/yw/index.html"
BASE_URL = "https://jw.nanjing.gov.cn"

# XPath配置
LIST_CONTAINER_XPATH = "//ul[@class='m_right_ul']"  # 新闻列表容器XPath
NEWS_ITEM_XPATH = "//ul[@class='m_right_ul']/li"  # 新闻项XPath
NEWS_TITLE_XPATH = ".//a"  # 新闻标题链接XPath
NEWS_DATE_XPATH = ".//span[last()]"  # 新闻日期XPath
NEXT_PAGE_XPATH = "//a[contains(text(), '下一页')]/@href"  # 下一页链接XPath

# 分页URL模式配置
PAGE_URL_PATTERN = "https://jw.nanjing.gov.cn/xwzx/yw/index_{}.html"  # 分页URL模式，{}为页码占位符
FIRST_PAGE_URL = "https://jw.nanjing.gov.cn/xwzx/yw/index.html"  # 第一页URL

# 爬取配置
MAX_PAGES = 15  # 最多爬取页数（增加页数以确保覆盖5-7月的所有新闻）
START_PAGE = 0  # 起始页码

# 目标日期范围配置
TARGET_DATE_RANGE = ['2025-05', '2025-06', '2025-07']  # 爬取5-7月的新闻

# 输出文件配置
OUTPUT_FILENAME = "南京市纪委监委_要闻动态_5-7月.xlsx"

# 请求头配置
HEADERS = {
    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
    'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
    'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
    'Accept-Encoding': 'gzip, deflate, br',
    'Connection': 'keep-alive',
    'Upgrade-Insecure-Requests': '1',
}

# 请求超时设置（秒）
REQUEST_TIMEOUT = 30

# 重试配置
MAX_RETRIES = 3  # 最大重试次数
RETRY_DELAY = 2  # 重试间隔（秒）

# SSL证书验证
VERIFY_SSL = True

# 代理设置
PROXIES = None

# 内容提取XPath配置（用于详情页）
CONTENT_XPATHS = [
    "//div[contains(@class, 'content')]//p",  # 正文段落
    "//div[@class='main_content']//p",  # 主要内容段落
    "//div[contains(text(), '来源：')]/../following-sibling::*",  # 来源信息后的内容
    "//body//p[string-length(text()) > 50]",  # 长度超过50字符的段落
]

# 标题提取XPath配置
TITLE_XPATHS = [
    "//h1",
    "//h2",
    "//title",
    "//div[@class='title']",
    "//div[contains(@class, 'title')]",
    "//div[contains(@class, 'content')]//h1",
    "//div[contains(@class, 'content')]//h2"
]

# 发表时间提取XPath配置
PUBLISH_TIME_XPATHS = [
    "//text()[contains(., '更新时间：')]/following-sibling::text()[contains(., '2025')]",  # 更新时间后的文本
    "//text()[contains(., '更新时间：')]",  # 包含更新时间的文本
    "//*[contains(text(), '更新时间：')]",  # 包含更新时间的元素
    "//span[contains(text(), '2025')]",  # 包含2025的span
    "//div[contains(text(), '来源：')]",  # 来源信息
    "//p[contains(text(), '2025')]",  # 包含2025的段落
]
