'''
Description: 
Date: 2025-07-17 19:05:54
Author: <PERSON><PERSON><PERSON>
LastEditTime: 2025-07-17 19:21:47
FilePath: \Spider_faxuan\config_3_1.py
'''
# -*- coding: utf-8 -*-
"""
南京市委组织部工作动态爬虫配置文件
专门适配: https://zzb.nanjing.gov.cn/lgbgz/gzdt/index.html
"""

# 基础URL配置
START_URL = "https://zzb.nanjing.gov.cn/lgbgz/gzdt/index.html"
BASE_URL = "https://zzb.nanjing.gov.cn"

# 工作动态页面特定XPath配置 - 修正版
LIST_CONTAINER_XPATH = "//ul"  # 新闻列表容器（更通用的选择器）
NEWS_ITEM_XPATH = "//li[contains(., '2025-')]"   # 包含2025年日期的li项
NEWS_TITLE_XPATH = ".//a"  # 新闻标题选择器（从li中提取a标签）
NEWS_DATE_XPATH = "./text()[contains(., '2025-')]"  # 直接匹配包含2025-的文本
NEXT_PAGE_XPATH = "//a[contains(text(), '下一页')]/@href"  # 通用下一页按钮

# 分页URL模式配置（支持多种格式探测）
PAGE_URL_PATTERNS = [
    "https://zzb.nanjing.gov.cn/lgbgz/gzdt/index_{}.html",  # 格式1: index_页码.html
    "https://zzb.nanjing.gov.cn/lgbgz/gzdt/index.html?page={}",  # 格式2: 参数分页
]
FIRST_PAGE_URL = "https://zzb.nanjing.gov.cn/lgbgz/gzdt/index.html"

# 爬取控制
MAX_PAGES = 25  # 组织部更新频率较低，增加页数以覆盖5-7月数据
START_PAGE = 1  # 从第1页开始

# 目标日期范围 - 工作动态2025年5-7月
TARGET_DATE_RANGE = [
    '2025-05', '2025-5',  # 支持带0和不带0的格式
    '2025-06', '2025-6',
    '2025-07', '2025-7',
    '05/2025', '5/2025',  # 美式日期格式
    '06/2025', '6/2025',
    '07/2025', '7/2025',
]

# 输出文件配置
OUTPUT_FILENAME = "spiderdata/南京市委组织部_工作动态_2025年5-7月.xlsx"

# 请求头配置（针对组织部站点微调）
HEADERS = {
    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
    'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8',
    'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
    'Accept-Encoding': 'gzip, deflate, br',
    'Connection': 'keep-alive',
    'Cache-Control': 'max-age=0',
    'Upgrade-Insecure-Requests': '1',
}

# 网络配置
REQUEST_TIMEOUT = 30
MAX_RETRIES = 3
RETRY_DELAY = 3  # 组织部服务器响应可能较慢，延长重试间隔
VERIFY_SSL = True
PROXIES = None

# 详情页内容提取配置（组织部文章结构可能与纪委不同）
CONTENT_XPATHS = [
    "//div[@class='content_text']//p",  # 组织部正文容器
    "//div[@class='TRS_Editor']//p",   # 政务系统通用编辑器
    "//div[@class='article-content']//p",  # 标准文章内容
    "//div[@id='zoom']//p",            # 武汉市政务标准
    "//div[@class='padding']//p",     # 备选通用路径
    "//body//p[string-length(text()) > 30]",  # 长文本段落保底
]

# 标题提取配置
TITLE_XPATHS = [
    "//h1",
    "//h2[@class='title']",
    "//div[@class='title']//h2", 
    "//div[@class='tit']",          # 组织部可能的标题类名
    "//title",                      # 页面标题保底
]

# 发布时间提取配置
PUBLISH_TIME_XPATHS = [
    "//span[@class='time']",  # 标准时间span
    "//span[@class='date']",
    "//div[@class='article-infos']//span",  # 文章信息区
    "//*[contains(text(), '发布时间')]",
    "//*[contains(text(), '发布日期')]",
    "//text()[contains(.,'2025-') or contains(.,'2025/') or contains(.,'2025年')]"
]